package goupload

import (
	"strconv"
	"time"

	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

// UploadResult 表示上传操作的结果
type UploadResult struct {
	Path         string        `json:"path"`          // 完整的URL路径 (如: "/useruploads/1225/abc12/file.pdf")
	Prefix       string        `json:"prefix"`        // URL前缀 (如: "/useruploads")
	Size         int64         `json:"size"`          // 文件大小（字节）
	WrittenPaths []WrittenPath `json:"written_paths"` // 所有写入位置详情
	Filename     string        `json:"filename"`      // 生成的新文件名
	MimeType     string        `json:"mime_type"`     // 检测到的MIME类型
}

// WrittenPath 表示文件写入位置的详细信息
type WrittenPath struct {
	Type     string            `json:"type"`             // 存储类型: "local" 或 "s3"
	Path     string            `json:"path"`             // 本地存储的完整物理路径 或 S3的key路径
	Size     int64             `json:"size"`             // 写入的文件大小
	Endpoint string            `json:"endpoint"`         // S3端点（仅S3类型）
	Bucket   string            `json:"bucket"`           // S3存储桶（仅S3类型）
	Target   string            `json:"target,omitempty"` // S3提供商名称 (仅S3类型)
	Metadata *S3ObjectMetadata `json:"metadata"`         // S3元数据（仅S3类型）
}

// WriteOptions 表示写入操作的配置选项
type WriteOptions struct {
	MaxRetries      int           `json:"max_retries"`      // 最大重试次数（默认: 3）
	RetryDelay      time.Duration `json:"retry_delay"`      // 重试延迟（默认: 1秒）
	S3Timeout       time.Duration `json:"s3_timeout"`       // S3操作超时（默认: 30秒）
	ChunkSize       int64         `json:"chunk_size"`       // S3分块上传大小（默认: 5MB）
	EnableLogging   bool          `json:"enable_logging"`   // 是否启用详细日志
	ValidateContent bool          `json:"validate_content"` // 是否验证文件内容（默认: true）
	EnableMetadata  bool          `json:"enable_metadata"`  // 是否启用S3元数据（默认: true）
	S3ACL           types.ObjectCannedACL
	ContentType     *string
}

// StatsUpdater 定义了目录统计更新器的接口
// 这使得在测试中可以轻松地模拟(mock)DirKeyStore
type StatsUpdater interface {
	AddDirStats(l1 string, l2 string, entityAmount int, fileAmount int)
}

// S3ObjectMetadata S3对象元数据
type S3ObjectMetadata struct {
	OriginalName string `json:"onm"`  // 原始文件名
	Timestamp    int64  `json:"ts"`   // 上传时间戳
	MimeType     string `json:"mime"` // MIME类型
	UserID       string `json:"uid"`  // 用户ID
	Tags         string `json:"tags"` // 标签
}

// ToS3Headers 将元数据转换为S3头部格式
func (m *S3ObjectMetadata) ToS3Headers() map[string]string {
	headers := make(map[string]string)

	if m.OriginalName != "" {
		headers["x-amz-meta-onm"] = m.OriginalName
	} else {
		// 原始文件名为空
	}
	if m.Timestamp != 0 {
		headers["x-amz-meta-ts"] = strconv.FormatInt(m.Timestamp, 10)
	} else {
		// 时间戳为0
	}
	if m.MimeType != "" {
		headers["x-amz-meta-mime"] = m.MimeType
	} else {
		// MIME类型为空
	}
	if m.UserID != "" {
		headers["x-amz-meta-uid"] = m.UserID
	} else {
		// 用户ID为空
	}
	if m.Tags != "" {
		headers["x-amz-meta-tags"] = m.Tags
	} else {
		// 标签为空
	}

	return headers
}

// FromS3Headers 从S3头部创建元数据
func (m *S3ObjectMetadata) FromS3Headers(headers map[string]string) {
	if onm, exists := headers["x-amz-meta-onm"]; exists {
		m.OriginalName = onm
	} else {
		// 没有原始文件名元数据
	}
	if ts, exists := headers["x-amz-meta-ts"]; exists {
		if timestamp, err := strconv.ParseInt(ts, 10, 64); err == nil {
			m.Timestamp = timestamp
		} else {
			// 时间戳解析失败
		}
	} else {
		// 没有时间戳元数据
	}
	if mime, exists := headers["x-amz-meta-mime"]; exists {
		m.MimeType = mime
	} else {
		// 没有MIME类型元数据
	}
	if uid, exists := headers["x-amz-meta-uid"]; exists {
		m.UserID = uid
	} else {
		// 没有用户ID元数据
	}
	if tags, exists := headers["x-amz-meta-tags"]; exists {
		m.Tags = tags
	} else {
		// 没有标签元数据
	}
}

// DefaultWriteOptions 返回默认的写入选项
func DefaultWriteOptions() WriteOptions {
	return WriteOptions{
		MaxRetries:      3,
		RetryDelay:      1 * time.Second,
		S3Timeout:       30 * time.Second,
		ChunkSize:       5 * 1024 * 1024, // 5MB
		EnableLogging:   false,
		ValidateContent: true,
		EnableMetadata:  true,
		S3ACL:           types.ObjectCannedACLPrivate,
	}
}
