package goupload

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	goconfig "github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"github.com/real-rm/gomongo"
)

var s3ClientCache sync.Map
var s3CacheCleanupOnce sync.Once

const (
	s3CacheCleanupInterval = 5 * time.Minute
)

// init 启动S3客户端缓存的后台清理任务
func init() {
	startS3CacheCleanupTask()
}

// startS3CacheCleanupTask 确保后台清理任务只被启动一次
func startS3CacheCleanupTask() {
	s3CacheCleanupOnce.Do(func() {
		golog.Info("starting background task for s3 client cache cleanup", "interval", s3CacheCleanupInterval)
		ticker := time.NewTicker(s3CacheCleanupInterval)
		go func() {
			for range ticker.C {
				cleanupStaleS3Clients()
			}
		}()
	})
}

// cleanupStaleS3Clients 是实际的清理函数。
// 它获取当前所有有效的S3配置，并从缓存中移除那些不再有效的客户端。
func cleanupStaleS3Clients() {
	s3Providers, err := levelStore.GetConnectionSources(nil)
	if err != nil {
		golog.Error("failed to get s3 connection sources for cache cleanup", "error", err)
		return
	}

	activeCacheKeys := make(map[string]bool)
	if s3Providers != nil {
		for _, p := range s3Providers.S3Providers {
			activeCacheKeys[p.Name+"@@"+p.Endpoint] = true
		}
	}

	s3ClientCache.Range(func(key, _ interface{}) bool {
		if _, ok := activeCacheKeys[key.(string)]; !ok {
			s3ClientCache.Delete(key)
			golog.Info("removed stale s3 client from cache", "key", key)
		}
		return true
	})
}

// s3API defines the interface for S3 operations we need (upload and delete).
// It's implemented by *s3.Client and can be mocked for tests.
type s3API interface {
	PutObject(ctx context.Context, params *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
	DeleteObject(ctx context.Context, params *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)
}

// getOrNewS3Client now returns the s3API interface.
func getOrNewS3Client(ctx context.Context, provider levelStore.S3ProviderConfig) (s3API, error) {
	cacheKey := provider.Name + "@@" + provider.Endpoint
	if client, ok := s3ClientCache.Load(cacheKey); ok {
		// The type assertion is now to the interface.
		return client.(s3API), nil
	}

	// Create new client logic remains the same...
	cfg, err := config.LoadDefaultConfig(ctx,
		config.WithRegion(provider.Region),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(provider.Key, provider.Pass, "")),
		config.WithEndpointResolverWithOptions(aws.EndpointResolverWithOptionsFunc(
			func(_, _ string, _ ...interface{}) (aws.Endpoint, error) {
				return aws.Endpoint{URL: provider.Endpoint}, nil
			})),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to load s3 config for provider %s: %w", provider.Name, err)
	}

	newClient := s3.NewFromConfig(cfg, func(o *s3.Options) {
		o.UsePathStyle = true // 使用路径样式 URL，适用于 S3 兼容存储如 GarageHQ
	})

	// 使用 LoadOrStore 原子性地加载或存储。
	// 如果在创建期间，另一个goroutine已经存入了客户端，actual 将是那个已存在的客户端。
	// 否则，actual 将是我们刚刚创建的 newClient。
	actual, loaded := s3ClientCache.LoadOrStore(cacheKey, newClient)
	if loaded {
		golog.Info("race condition averted: another goroutine created the s3 client first", "key", cacheKey)
	}

	return actual.(s3API), nil
}

const (
	// SINGLE_FILE_MAX_LIMIT 为普通单文件上传定义了最大大小。
	SINGLE_FILE_MAX_LIMIT int64 = 500 * 1024 * 1024 // 500 MB
	// CHUNKED_UPLOAD_MAX_LIMIT 为分块上传定义了最大总大小。
	CHUNKED_UPLOAD_MAX_LIMIT int64 = 10 * 1024 * 1024 * 1024 // 10 GB
	// MIME_DETECTION_BUFFER_SIZE 定义了用于MIME类型检测的缓冲区大小。
	MIME_DETECTION_BUFFER_SIZE = 512
)

// uploadPrerequisites 包含了执行一次上传所需的所有预先计算好的数据。
type uploadPrerequisites struct {
	Config       *levelStore.UserUploadConfig
	RelativePath string
	NewFilename  string
	Metadata     map[string]string
}

// ErrFileTooLarge 表示文件大小超过允许的最大限制
var ErrFileTooLarge = errors.New("file size exceeds the maximum allowed limit")

// Upload 高级API函数，提供完整的文件上传流程
// 自动获取用户配置，处理 userupload 部分，提取 WriteOptions 配置，并使用传入的StatsUpdater更新统计
func Upload(ctx context.Context, statsUpdater StatsUpdater, site, entryName, uid string, reader io.Reader, originalFilename string, clientDeclaredSize int64) (*UploadResult, error) {

	provider := &LevelStoreProvider{}
	prereqs, err := prepareUploadPrerequisites(provider, site, entryName, uid, originalFilename)
	if err != nil {
		return nil, err
	}

	// 2. 确定最终生效的上传大小限制
	effectiveLimit := determineEffectiveLimit(prereqs.Config.MaxSize, SINGLE_FILE_MAX_LIMIT)

	// 3. 快速拒绝：如果客户端声明的大小超过了生效的限制，立即拒绝
	if clientDeclaredSize > 0 && clientDeclaredSize > effectiveLimit {
		return nil, fmt.Errorf("declared file size %d bytes exceeds limit %d bytes: %w",
			clientDeclaredSize, effectiveLimit, ErrFileTooLarge)
	}

	// 4. 从用户配置中提取全局的 WriteOptions（不分 type）
	writeOpts := extractWriteOptions()

	// 4.1. 获取S3连接配置（如果需要）
	s3ProviderMap, err := getS3ProviderMap(ctx, prereqs.Config.Storage)
	if err != nil {
		return nil, err // 错误已经被包装
	}

	// 5. 收集文件信息并获取可重复读取的reader（传入生效的限制）
	rewindableReader, fileInfo, cleanup, err := getFileInfoWithRewindableReader(reader, originalFilename, effectiveLimit, prereqs.Config.TmpPath)
	if cleanup != nil {
		defer cleanup()
	}
	if err != nil {
		// 对于文件信息收集失败的错误，直接包装返回
		return nil, fmt.Errorf("failed to collect file info: %w", err)
	}

	// 6. 执行事务性写入
	writtenPaths, err := WriteToLocationsWithRollback(ctx, prereqs.Config, s3ProviderMap, rewindableReader, prereqs.RelativePath, writeOpts, prereqs.Metadata, fileInfo)
	if err != nil {
		return nil, err // WriteToLocationsWithRollback已经返回适当的UploadError
	}

	// 7. 更新目录统计信息 (使用传入的statsUpdater)
	if statsUpdater != nil {
		// 从相对路径中提取L1和L2
		parts := strings.Split(prereqs.RelativePath, "/")
		if len(parts) >= 2 {
			l1 := parts[0]
			l2 := parts[1]
			statsUpdater.AddDirStats(l1, l2, 1, 1) // 增加一个实体和一个文件
		} else {
			golog.Warn("Failed to update directory statistics due to invalid path",
				"site", site, "path", prereqs.RelativePath)
		}
	} else {
		golog.Warn("StatsUpdater is nil, skipping statistics update", "site", site)
	}

	// 8. 返回结果
	// 最终返回的路径包含 prefix，用于构建完整的访问URL
	return &UploadResult{
		Path:         prereqs.RelativePath,
		Prefix:       prereqs.Config.Prefix,
		Size:         fileInfo.Size,
		WrittenPaths: writtenPaths,
		Filename:     prereqs.NewFilename,
		MimeType:     fileInfo.MimeType,
	}, nil
}

// PathAndConfigProvider 定义了获取配置和路径组件的依赖项接口。
// 这使得在测试中可以模拟(mock)这些依赖。
type PathAndConfigProvider interface {
	GetUserUploadConfig(site, entryName string) (*levelStore.UserUploadConfig, error)
	GetPathComponents(t time.Time, site, seed string) (*levelStore.PathResult, error)
	GenerateFilename(originalFilename string) (string, error)
	// UpdateDirStats(site, l1, l2 string, entityAmount, fileAmount int) error // REMOVED
}

// LevelStoreProvider 是 PathAndConfigProvider 接口的生产环境实现。
// 它直接调用 levelStore 包中的函数。
type LevelStoreProvider struct{}

// GetUserUploadConfig 调用 levelStore 来获取用户上传配置。
func (p *LevelStoreProvider) GetUserUploadConfig(site, entryName string) (*levelStore.UserUploadConfig, error) {
	return levelStore.GetUserUploadConfig(site, entryName, nil)
}

// GetPathComponents 调用 levelStore 来获取路径组件。
func (p *LevelStoreProvider) GetPathComponents(t time.Time, site, seed string) (*levelStore.PathResult, error) {
	return levelStore.GetPathComponents(t, site, seed)
}

// GenerateFilename 调用 goupload 包中的 GenerateFilename 函数。
func (p *LevelStoreProvider) GenerateFilename(originalFilename string) (string, error) {
	return GenerateFilename(originalFilename)
}

// determineEffectiveLimit 根据业务配置和服务器硬限制，计算出最终生效的大小限制。
func determineEffectiveLimit(configMaxSize string, serverHardLimit int64) int64 {
	effectiveLimit := serverHardLimit
	if configMaxSize != "" {
		businessLimit, err := parseSize(configMaxSize)
		if err != nil {
			golog.Warn("invalid MaxSize format in config, using server default", "maxSize", configMaxSize, "error", err)
		} else if businessLimit > 0 && businessLimit < effectiveLimit {
			effectiveLimit = businessLimit
		}
	}
	return effectiveLimit
}

// prepareUploadPrerequisites 负责处理所有上传操作开始前的准备工作。
// 它包括：获取配置、验证、生成路径和文件名、准备元数据。
// 这是一个内部辅助函数，被 Upload 和未来的 InitiateChunkedUpload 共同调用。
func prepareUploadPrerequisites(provider PathAndConfigProvider, site, entryName, uid, originalFilename string) (*uploadPrerequisites, error) {
	// 1. 获取上传策略
	config, err := provider.GetUserUploadConfig(site, entryName)
	if err != nil {
		return nil, fmt.Errorf("failed to get upload config: %w", err)
	}

	// 2. 校验文件名
	if err := ValidateFilename(originalFilename); err != nil {
		return nil, err // ValidateFilename已经返回UploadError
	}

	// 3. 生成路径
	timestamp := time.Now().Unix()
	pathSeed := GeneratePathSeed(originalFilename, timestamp, uid)
	pathResult, err := provider.GetPathComponents(time.Now(), site, pathSeed)
	if err != nil {
		return nil, fmt.Errorf("failed to generate path: %w", err)
	}

	// 4. 生成新文件名
	newFilename, err := provider.GenerateFilename(originalFilename)
	if err != nil {
		// 如果无法生成唯一文件名，这是一个关键错误，应立即中止。
		return nil, fmt.Errorf("failed to generate unique filename: %w", err)
	}
	relativePath := pathResult.Combined + "/" + newFilename

	// 5. 准备元数据
	metadata := map[string]string{
		"original-filename": originalFilename,
		"user-id":           uid,
		"upload-time":       strconv.FormatInt(timestamp, 10),
	}

	// 6. 打包并返回所有准备好的数据
	return &uploadPrerequisites{
		Config:       config,
		RelativePath: relativePath,
		NewFilename:  newFilename,
		Metadata:     metadata,
	}, nil
}

// WriteToLocationsWithRollback 核心的事务性写入函数
// 适用于需要自定义路径或批量上传的场景
func WriteToLocationsWithRollback(ctx context.Context, config *levelStore.UserUploadConfig, s3ProviderMap map[string]levelStore.S3ProviderConfig, reader io.ReadSeeker, relativePath string, opts WriteOptions, metadata map[string]string, fileInfo *FileInfo) ([]WrittenPath, error) {
	var writtenPaths []WrittenPath
	var allErrors []error

	// 1. 预热本次上传需要的S3客户端
	s3ClientMap := make(map[string]s3API)
	for name, p := range s3ProviderMap {
		client, err := getOrNewS3Client(ctx, p)
		if err != nil {
			// 快速失败：如果任何一个S3客户端因配置错误而无法初始化，
			// 整个上传操作都无法完成，应立即中止。
			return nil, fmt.Errorf("failed to initialize s3 client for provider '%s': %w", name, err)
		}
		s3ClientMap[name] = client
	}

	// 2. 遍历所有存储目标并执行写入
	for _, storage := range config.Storage {
		// 在每次写入前，将reader重置到开头
		if _, err := reader.Seek(0, io.SeekStart); err != nil {
			// 如果连reader都无法重置，这是一个严重错误，直接返回
			return nil, fmt.Errorf("failed to seek reader for %s storage: %w", storage.Type, err)
		}

		switch storage.Type {
		case "local":
			fullPath := filepath.Join(storage.Path, relativePath)
			err := writeFileToLocalPath(reader, fullPath)
			if err != nil {
				allErrors = append(allErrors, fmt.Errorf("failed to write to local path %s: %w", fullPath, err))
			} else {
				writtenPaths = append(writtenPaths, WrittenPath{
					Type: "local",
					Path: fullPath,
					Size: fileInfo.Size,
				})
			}

		case "s3":
			var err error
			// 查找S3提供商配置
			provider, ok := s3ProviderMap[storage.Target]
			if !ok {
				err = fmt.Errorf("s3 provider '%s' not found in connection sources", storage.Target)
			} else {
				// 获取缓存的S3客户端
				s3Client, clientOk := s3ClientMap[storage.Target]
				if !clientOk {
					err = fmt.Errorf("s3 client for provider '%s' was not initialized", storage.Target)
				} else {
					// 为S3准备元数据
					s3Metadata := make(map[string]string)
					for k, v := range metadata {
						s3Metadata[k] = v
					}
					s3Metadata["mime-type"] = fileInfo.MimeType

					err = writeFileToS3(ctx, s3Client, reader, storage.Bucket, relativePath, opts, s3Metadata)
				}
			}

			if err != nil {
				allErrors = append(allErrors, fmt.Errorf("failed to write to S3 target '%s': %w", storage.Target, err))
			} else {
				// 写入成功，创建并填充S3元数据结构
				s3Meta := &S3ObjectMetadata{
					OriginalName: metadata["original-filename"],
					UserID:       metadata["user-id"],
					MimeType:     fileInfo.MimeType,
				}
				if tsStr, ok := metadata["upload-time"]; ok {
					if ts, err := strconv.ParseInt(tsStr, 10, 64); err == nil {
						s3Meta.Timestamp = ts
					}
				}

				writtenPaths = append(writtenPaths, WrittenPath{
					Type:     "s3",
					Path:     relativePath,
					Size:     fileInfo.Size,
					Endpoint: provider.Endpoint,
					Bucket:   storage.Bucket,
					Target:   storage.Target, // 记录target，用于回滚
					Metadata: s3Meta,
				})
			}
		}

		// 如果出现任何错误，立即停止并准备回滚
		if len(allErrors) > 0 {
			break
		}
	}

	// 3. 如果出现错误，执行回滚
	if len(allErrors) > 0 {
		rollbackErrors := rollbackWrittenFiles(ctx, writtenPaths, s3ProviderMap, s3ClientMap, opts)
		if len(rollbackErrors) > 0 {
			// 如果回滚也失败了，返回一个特殊的错误
			return writtenPaths, fmt.Errorf("write failed and rollback had %d errors: %w",
				len(rollbackErrors), allErrors[0])
		}
		// 如果回滚成功，返回空的writtenPaths以明确表示最终状态
		var failedStage string
		failedConfigIndex := len(writtenPaths)
		if failedConfigIndex < len(config.Storage) {
			failedStorage := config.Storage[failedConfigIndex]
			failedStage = failedStorage.Type
		} else {
			// This case should not be reached if there is an error,
			// but as a fallback, report a generic stage.
			failedStage = "unknown"
		}

		return []WrittenPath{}, fmt.Errorf("write to %s failed: %w", failedStage, allErrors[0])
	}

	return writtenPaths, nil
}

// getS3ProviderMap checks if S3 storage is needed and fetches the connection sources.
func getS3ProviderMap(_ context.Context, storage []levelStore.StorageConfig) (map[string]levelStore.S3ProviderConfig, error) {
	needsS3 := false
	for _, s := range storage {
		if s.Type == "s3" {
			needsS3 = true
			break
		}
	}

	if !needsS3 {
		return nil, nil
	}

	s3Providers, err := levelStore.GetConnectionSources(nil)
	if err != nil {
		return nil, fmt.Errorf("failed to get s3 connection sources: %w", err)
	}

	s3ProviderMap := make(map[string]levelStore.S3ProviderConfig)
	if s3Providers != nil {
		for _, p := range s3Providers.S3Providers {
			s3ProviderMap[p.Name] = p
		}
	}
	return s3ProviderMap, nil
}

// writeFileToLocalPath 将文件写入本地路径，支持原子性写入
func writeFileToLocalPath(reader io.Reader, fullPath string) error {
	// 确保目标目录存在
	dir := filepath.Dir(fullPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}

	// 创建临时文件（在同一目录下，确保原子性重命名）
	tempFile, err := os.CreateTemp(dir, ".tmp_upload_*")
	if err != nil {
		return fmt.Errorf("failed to create temp file: %w", err)
	}
	tempPath := tempFile.Name()
	committed := false // 标志位，用于判断是否已成功提交

	// 确保清理临时文件
	defer func() {
		tempFile.Close()
		if !committed {
			if err := os.Remove(tempPath); err != nil && !os.IsNotExist(err) {
				// 记录清理失败，但不影响主要错误
				golog.Warn("Failed to remove temporary file on failure", "path", tempPath, "error", err)
			}
		}
	}()

	// 写入数据到临时文件
	_, err = io.Copy(tempFile, reader)
	if err != nil {
		return fmt.Errorf("failed to write data to temp file: %w", err)
	}

	// 确保数据写入磁盘
	if err := tempFile.Sync(); err != nil {
		return fmt.Errorf("failed to sync temp file: %w", err)
	}

	// 在重命名前必须关闭文件句柄
	if err := tempFile.Close(); err != nil {
		return fmt.Errorf("failed to close temp file before rename: %w", err)
	}

	// 原子性重命名
	if err := os.Rename(tempPath, fullPath); err != nil {
		return fmt.Errorf("failed to rename temp file to final path: %w", err)
	}

	committed = true // 重命名成功，标记为已提交
	return nil
}

// writeFileToS3 将文件写入到S3兼容的对象存储
func writeFileToS3(ctx context.Context, s3Client s3API, reader io.ReadSeeker, bucket, key string, opts WriteOptions, metadata map[string]string) error {
	operation := func() error {
		// 每次重试都必须将 reader 重置到开头
		if _, err := reader.Seek(0, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek reader for s3 upload retry: %w", err)
		}

		// 为单次S3操作创建带超时的上下文
		timeoutCtx, cancel := context.WithTimeout(ctx, opts.S3Timeout)
		defer cancel()

		putObjectInput := &s3.PutObjectInput{
			Bucket:      aws.String(bucket),
			Key:         aws.String(key),
			Body:        reader,
			ACL:         opts.S3ACL,
			ContentType: aws.String(metadata["mime-type"]),
			Metadata:    metadata,
		}

		_, err := s3Client.PutObject(timeoutCtx, putObjectInput)
		return err // 直接返回AWS SDK的错误
	}

	err := ExecuteWithRetry(operation, opts.MaxRetries, opts.RetryDelay)
	if err != nil {
		return fmt.Errorf("failed to upload to s3 after %d retries: %w", opts.MaxRetries, err)
	}
	return nil
}

// cleanupLocalFile 负责删除本地文件。
func cleanupLocalFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		// 文件不存在，认为清理成功
		return nil
	}

	// 删除文件
	if err := os.Remove(filePath); err != nil {
		return fmt.Errorf("failed to remove file %s: %w", filePath, err)
	}

	return nil
}

// rollbackWrittenFiles now accepts a map of s3API interfaces.
func rollbackWrittenFiles(ctx context.Context, writtenPaths []WrittenPath, _ map[string]levelStore.S3ProviderConfig, s3ClientMap map[string]s3API, opts WriteOptions) []error {
	var rollbackErrors []error
	var wg sync.WaitGroup
	var mu sync.Mutex // To safely append to rollbackErrors from multiple goroutines

	for _, p := range writtenPaths {
		wg.Add(1)
		go func(path WrittenPath) {
			defer wg.Done()
			var err error
			switch path.Type {
			case "local":
				err = cleanupLocalFile(path.Path)
			case "s3":
				// Find the S3 client from the pre-warmed map
				s3Client, ok := s3ClientMap[path.Target]
				if !ok {
					// This should ideally not happen if write was successful
					err = fmt.Errorf("s3 client for target '%s' not found during rollback", path.Target)
				} else {
					err = deleteS3Object(ctx, s3Client, path.Bucket, path.Path, opts)
				}
			}
			if err != nil {
				mu.Lock()
				rollbackErrors = append(rollbackErrors, fmt.Errorf("rollback failed for %s path %s: %w", path.Type, path.Path, err))
				mu.Unlock()
			}
		}(p)
	}

	wg.Wait()
	return rollbackErrors
}

func deleteS3Object(ctx context.Context, s3Client s3API, bucket, key string, opts WriteOptions) error {
	operation := func() error {
		timeoutCtx, cancel := context.WithTimeout(ctx, opts.S3Timeout)
		defer cancel()

		_, err := s3Client.DeleteObject(timeoutCtx, &s3.DeleteObjectInput{
			Bucket: aws.String(bucket),
			Key:    aws.String(key),
		})
		return err
	}
	return ExecuteWithRetry(operation, opts.MaxRetries, opts.RetryDelay)
}

// extractWriteOptions 从用户配置中提取全局的 WriteOptions（不分 type）
func extractWriteOptions() WriteOptions {
	// Start with default options
	opts := DefaultWriteOptions()

	// Get global write options from config
	globalOptionsConfig := goconfig.Config("write_options")
	if globalOptions, ok := globalOptionsConfig.(map[string]interface{}); ok {
		opts = applyConfigToWriteOptions(opts, globalOptions)
		golog.Info("extracted global write options", "options", opts)
	} else {
		golog.Info("no write_options config found, using defaults", "options", opts)
	}

	return opts
}

func applyConfigToWriteOptions(opts WriteOptions, config map[string]interface{}) WriteOptions {
	if v, ok := config["max_retries"].(int64); ok {
		opts.MaxRetries = int(v)
	}
	if v, ok := config["retry_delay"].(string); ok {
		if d, err := time.ParseDuration(v); err == nil {
			opts.RetryDelay = d
		} else {
			golog.Warn("failed to parse retry_delay duration", "value", v, "error", err)
		}
	}
	if v, ok := config["s3_timeout"].(string); ok {
		if d, err := time.ParseDuration(v); err == nil {
			opts.S3Timeout = d
		} else {
			golog.Warn("failed to parse s3_timeout duration", "value", v, "error", err)
		}
	}
	if v, ok := config["chunk_size"].(int64); ok {
		opts.ChunkSize = v
	}
	if v, ok := config["enable_logging"].(bool); ok {
		opts.EnableLogging = v
	}
	if v, ok := config["validate_content"].(bool); ok {
		opts.ValidateContent = v
	}
	if v, ok := config["enable_metadata"].(bool); ok {
		opts.EnableMetadata = v
	}
	return opts
}

// getFileInfoWithRewindableReader 检查传入的reader，如果不是可重复读的，
// 则将其安全地流式传输到磁盘上的临时文件，并返回该文件的seeker。
// 它还会返回一个清理函数，调用者必须在操作完成后执行该函数。
func getFileInfoWithRewindableReader(reader io.Reader, filename string, maxAllowedSize int64, tempDir string) (io.ReadSeeker, *FileInfo, func(), error) {
	// 1. 如果已经是ReadSeeker（例如*os.File），直接使用
	if rs, ok := reader.(io.ReadSeeker); ok {
		// 检查文件大小是否超限
		size, err := rs.Seek(0, io.SeekEnd)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("failed to seek to end: %w", err)
		}
		if size > maxAllowedSize {
			return nil, nil, nil, fmt.Errorf("file size exceeds limit: %w", ErrFileTooLarge)
		}
		// 同时检查文件大小是否为0
		if size <= 0 {
			return nil, nil, nil, fmt.Errorf("file size cannot be zero")
		}
		if _, err := rs.Seek(0, io.SeekStart); err != nil {
			return nil, nil, nil, fmt.Errorf("failed to seek to start: %w", err)
		}

		// 收集文件信息
		fileInfo, err := collectFileInfo(rs, filename)
		if err != nil {
			return nil, nil, nil, fmt.Errorf("failed to collect file info from seeker: %w", err)
		}
		fileInfo.Size = size

		// 再次将指针移到开头，因为collectFileInfo也消耗了流
		if _, err := rs.Seek(0, io.SeekStart); err != nil {
			return nil, nil, nil, fmt.Errorf("failed to rewind after collecting info: %w", err)
		}

		return rs, fileInfo, nil, nil // 不需要清理
	}

	// 2. 如果是普通reader（如http.Request.Body），则流式传输到临时文件
	tempFile, err := os.CreateTemp(tempDir, "upload-*.tmp")
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to create temp file: %w", err)
	}

	// 定义清理函数，用于关闭和删除临时文件
	cleanup := func() {
		tempFile.Close()
		if err := os.Remove(tempFile.Name()); err != nil && !os.IsNotExist(err) {
			golog.Warn("Failed to remove temporary file for non-seeker", "path", tempFile.Name(), "error", err)
		}
	}

	// 使用LimitedReader防止DoS攻击
	limitedReader := &io.LimitedReader{R: reader, N: maxAllowedSize}

	// 将数据从流拷贝到临时文件
	_, err = io.Copy(tempFile, limitedReader)
	if err != nil {
		cleanup()
		return nil, nil, nil, fmt.Errorf("failed to copy data to temp file: %w", err)
	}

	// 检查是否已达到限制。尝试多读取一个字节。
	// 如果成功，说明原始文件大于限制。
	oneMoreByte := make([]byte, 1)
	n, _ := reader.Read(oneMoreByte)
	if n > 0 {
		cleanup()
		return nil, nil, nil, fmt.Errorf("file size exceeds limit: %w", ErrFileTooLarge)
	}

	// 从临时文件的元数据中获取确切大小
	stat, err := tempFile.Stat()
	if err != nil {
		cleanup()
		return nil, nil, nil, fmt.Errorf("failed to get temp file stats: %w", err)
	}
	fileSize := stat.Size()

	// 检查文件大小是否为0
	if fileSize <= 0 {
		cleanup()
		return nil, nil, nil, fmt.Errorf("file size cannot be zero")
	}

	// 为了收集信息，将临时文件指针移回开头
	if _, err := tempFile.Seek(0, io.SeekStart); err != nil {
		cleanup()
		return nil, nil, nil, fmt.Errorf("failed to rewind temp file: %w", err)
	}

	// 现在我们有了一个可重复读的reader（临时文件），收集文件信息
	fileInfo, err := collectFileInfo(tempFile, filename)
	if err != nil {
		cleanup()
		return nil, nil, nil, fmt.Errorf("failed to collect file info from temp file: %w", err)
	}
	fileInfo.Size = fileSize

	// 为了实际的上传操作，再次将临时文件指针移回开头
	if _, err := tempFile.Seek(0, io.SeekStart); err != nil {
		cleanup()
		return nil, nil, nil, fmt.Errorf("failed to rewind temp file for upload: %w", err)
	}

	return tempFile, fileInfo, cleanup, nil
}

// collectFileInfo 从reader中读取少量字节来检测MIME类型和计算哈希
// 注意：这个函数会消耗reader的一部分，调用者需要负责重置（如果可能）
func collectFileInfo(reader io.Reader, _ string) (*FileInfo, error) {
	// 创建一个缓冲区来存储文件头部
	buffer := make([]byte, MIME_DETECTION_BUFFER_SIZE)
	n, err := reader.Read(buffer)
	if err != nil && err != io.EOF {
		return nil, fmt.Errorf("failed to read file header for mime type detection: %w", err)
	}

	// 检测MIME类型
	mimeType := http.DetectContentType(buffer[:n])
	// http.DetectContentType 对于纯文本文件会返回 "text/plain; charset=utf-8"
	// 我们通常只需要 "text/plain" 部分
	if finalMime, _, err := mime.ParseMediaType(mimeType); err == nil {
		mimeType = finalMime
	}

	// 注意：由于我们已经从reader中读取了数据，所以这里的size将不准确。
	// 正确的大小应该在 getFileInfoWithRewindableReader 中处理，
	// 那里有对整个可重复读取流的访问权。
	return &FileInfo{
		Size:     0, // Size is determined later
		MimeType: mimeType,
	}, nil
}

// FileInfo 包含了文件的基本元数据
type FileInfo struct {
	Size     int64  `json:"size"`      // 文件大小
	MimeType string `json:"mime_type"` // MIME类型
}

// NewStatsUpdater 自动初始化并返回 StatsUpdater（DirKeyStore），并自动设置 customDirs
func NewStatsUpdater(site string, entryName string, coll *gomongo.MongoCollection) (StatsUpdater, error) {
	// 使用新的 NewDirKeyStore API
	// 根据新的API签名：NewDirKeyStore(site, mongoCollection, entryName, [updateInterval])
	// 所有使用goupload的用户都应该传入具体的entryName，不使用空字符串
	// 使用默认的更新间隔（不传最后一个参数）

	store, err := levelStore.NewDirKeyStore(site, coll, entryName)
	if err != nil {
		return nil, err
	}

	// 获取用户上传配置并设置自定义目录
	config, err := levelStore.GetUserUploadConfig(site, entryName, nil)
	if err == nil && config != nil {
		localDirs := []string{}

		// 支持新的 Storage 配置格式
		for _, storage := range config.Storage {
			if storage.Type == "local" && storage.Path != "" {
				localDirs = append(localDirs, storage.Path)
			}
		}

		// 注意：新版本的UserUploadConfig只使用Storage配置格式
		// 如果需要向后兼容，可以在这里添加对旧格式的支持

		if len(localDirs) > 0 {
			// 直接调用SetCustomDirectories方法
			store.SetCustomDirectories(localDirs)
		}
	}
	return store, nil
}
