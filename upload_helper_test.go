package goupload

import (
	"errors"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 第一个测试：测试GenerateFilename成功生成文件名
func TestGenerateFilename_Success(t *testing.T) {
	// 测试带扩展名的文件
	filename, err := GenerateFilename("test.txt")
	assert.NoError(t, err, "生成文件名应该成功")
	assert.NotEmpty(t, filename, "文件名不应该为空")
	assert.True(t, strings.HasSuffix(filename, ".txt"), "应该保留原始扩展名")
	assert.True(t, len(filename) > 4, "生成的文件名应该包含ID和扩展名")

	// 测试不带扩展名的文件
	filename2, err := GenerateFilename("README")
	assert.NoError(t, err, "生成文件名应该成功")
	assert.NotEmpty(t, filename2, "文件名不应该为空")
	assert.False(t, strings.Contains(filename2, "."), "不应该有扩展名")

	// 测试空文件名
	filename3, err := GenerateFilename("")
	assert.NoError(t, err, "生成文件名应该成功")
	assert.NotEmpty(t, filename3, "文件名不应该为空")

	// 测试多个调用产生不同结果
	filename4, err := GenerateFilename("test.txt")
	assert.NoError(t, err, "生成文件名应该成功")
	assert.NotEqual(t, filename, filename4, "多次调用应该产生不同的文件名")
}

// 第二个测试：测试GenerateFilename处理复杂扩展名
func TestGenerateFilename_ComplexExtensions(t *testing.T) {
	testCases := []struct {
		input    string
		expected string
	}{
		{"file.tar.gz", ".gz"},
		{"archive.zip", ".zip"},
		{"image.jpeg", ".jpeg"},
		{"data.json", ".json"},
		{"script.sh", ".sh"},
		{"file.backup.old", ".old"},
	}

	for _, tc := range testCases {
		filename, err := GenerateFilename(tc.input)
		assert.NoError(t, err, "生成文件名应该成功: %s", tc.input)
		assert.True(t, strings.HasSuffix(filename, tc.expected),
			"文件名应该以正确的扩展名结尾: %s -> %s", tc.input, filename)
	}
}

// 第三个测试：测试ValidateFilename成功的情况
func TestValidateFilename_Success(t *testing.T) {
	validFilenames := []string{
		"file.txt",
		"document.pdf",
		"image-2023.jpg",
		"my_file_name.docx",
		"report (final).xlsx",
		"data.2023.csv",
		"script_v1.0.py",
		"README",
		"config.yaml",
		"测试文件.txt", // Unicode文件名
		"file with spaces.doc",
		"123456.zip",
		"a.b",
		strings.Repeat("a", 251) + ".txt", // 255个字符，接近最大长度
	}

	for _, filename := range validFilenames {
		err := ValidateFilename(filename)
		assert.NoError(t, err, "文件名应该验证成功: %s", filename)
	}
}

// 第四个测试：测试ValidateFilename空文件名
func TestValidateFilename_EmptyFilename(t *testing.T) {
	err := ValidateFilename("")
	assert.Error(t, err, "空文件名应该验证失败")
	assert.Contains(t, err.Error(), "cannot be empty", "错误信息应该包含空文件名信息")
}

// 第五个测试：测试ValidateFilename文件名过长
func TestValidateFilename_TooLong(t *testing.T) {
	longFilename := strings.Repeat("a", 256) + ".txt"
	err := ValidateFilename(longFilename)
	assert.Error(t, err, "过长文件名应该验证失败")
	assert.Contains(t, err.Error(), "cannot exceed 255 characters", "错误信息应该包含长度限制")
}

// 第六个测试：测试ValidateFilename非法字符
func TestValidateFilename_IllegalCharacters(t *testing.T) {
	illegalChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}

	for _, char := range illegalChars {
		filename := "test" + char + "file.txt"
		err := ValidateFilename(filename)
		assert.Error(t, err, "包含非法字符的文件名应该验证失败: %s", filename)
		assert.Contains(t, err.Error(), "illegal character", "错误信息应该包含非法字符信息")
		assert.Contains(t, err.Error(), char, "错误信息应该包含具体的非法字符")
	}
}

// 第七个测试：测试ValidateFilename Windows保留名称
func TestValidateFilename_ReservedNames(t *testing.T) {
	reservedNames := []string{
		"CON", "PRN", "AUX", "NUL",
		"COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9",
		"LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9",
	}

	for _, name := range reservedNames {
		// 测试大写
		err := ValidateFilename(name)
		assert.Error(t, err, "保留名称应该验证失败: %s", name)
		assert.Contains(t, err.Error(), "reserved name", "错误信息应该包含保留名称信息")

		// 测试小写
		err = ValidateFilename(strings.ToLower(name))
		assert.Error(t, err, "小写保留名称应该验证失败: %s", strings.ToLower(name))

		// 测试带扩展名（应该失败，因为只检查主文件名）
		err = ValidateFilename(name + ".txt")
		assert.Error(t, err, "带扩展名的保留名称应该验证失败: %s.txt", name)

		// 测试混合大小写
		mixedCase := ""
		for i, r := range name {
			if i%2 == 0 {
				mixedCase += strings.ToLower(string(r))
			} else {
				mixedCase += strings.ToUpper(string(r))
			}
		}
		err = ValidateFilename(mixedCase)
		assert.Error(t, err, "混合大小写保留名称应该验证失败: %s", mixedCase)
	}
}

// 第八个测试：测试ValidateFilename非保留名称
func TestValidateFilename_NonReservedNames(t *testing.T) {
	nonReservedNames := []string{
		"CONSOLE",  // 类似但不是保留名称
		"COM10",    // 超出范围
		"LPT0",     // 超出范围
		"CONABC",   // 包含但不完全匹配
		"COM",      // 不完整
		"MY_CON",   // 前缀
		"CON_FILE", // 后缀
	}

	for _, name := range nonReservedNames {
		err := ValidateFilename(name)
		assert.NoError(t, err, "非保留名称应该验证成功: %s", name)
	}
}

// 第九个测试：测试ExecuteWithRetry成功的情况
func TestExecuteWithRetry_Success(t *testing.T) {
	callCount := 0
	operation := func() error {
		callCount++
		return nil
	}

	err := ExecuteWithRetry(operation, 3, 10*time.Millisecond)
	assert.NoError(t, err, "成功操作应该立即返回")
	assert.Equal(t, 1, callCount, "成功操作只应该被调用一次")
}

// 第十个测试：测试ExecuteWithRetry重试后成功
func TestExecuteWithRetry_SuccessAfterRetries(t *testing.T) {
	callCount := 0
	operation := func() error {
		callCount++
		if callCount < 3 {
			return errors.New("temporary failure")
		}
		return nil
	}

	start := time.Now()
	err := ExecuteWithRetry(operation, 5, 5*time.Millisecond)
	elapsed := time.Since(start)

	assert.NoError(t, err, "重试后成功的操作应该返回nil")
	assert.Equal(t, 3, callCount, "操作应该被调用3次")
	assert.True(t, elapsed >= 10*time.Millisecond, "应该有重试延迟")
}

// 第十一个测试：测试ExecuteWithRetry达到最大重试次数
func TestExecuteWithRetry_MaxRetriesReached(t *testing.T) {
	callCount := 0
	expectedError := errors.New("persistent failure")

	operation := func() error {
		callCount++
		return expectedError
	}

	start := time.Now()
	err := ExecuteWithRetry(operation, 3, 5*time.Millisecond)
	elapsed := time.Since(start)

	assert.Error(t, err, "应该返回错误")
	assert.Equal(t, expectedError, err, "应该返回最后一次的错误")
	assert.Equal(t, 4, callCount, "操作应该被调用4次（初始+3次重试）")
	assert.True(t, elapsed >= 15*time.Millisecond, "应该有3次重试延迟")
}

// 第十二个测试：测试ExecuteWithRetry零重试次数
func TestExecuteWithRetry_ZeroRetries(t *testing.T) {
	callCount := 0
	expectedError := errors.New("failure")

	operation := func() error {
		callCount++
		return expectedError
	}

	err := ExecuteWithRetry(operation, 0, time.Millisecond)
	assert.Error(t, err, "应该返回错误")
	assert.Equal(t, expectedError, err, "应该返回操作的错误")
	assert.Equal(t, 1, callCount, "操作应该被调用1次")
}

// 第十三个测试：测试ExecuteWithRetry负数重试次数
func TestExecuteWithRetry_NegativeRetries(t *testing.T) {
	callCount := 0
	expectedError := errors.New("failure")

	operation := func() error {
		callCount++
		return expectedError
	}

	// 负数重试次数时，循环条件 i <= maxRetries 不满足，所以不会调用操作
	err := ExecuteWithRetry(operation, -1, time.Millisecond)
	assert.NoError(t, err, "负数重试次数应该返回nil（没有执行操作）")
	assert.Equal(t, 0, callCount, "操作不应该被调用")
}

// 第十四个测试：测试GeneratePathSeed
func TestGeneratePathSeed(t *testing.T) {
	testCases := []struct {
		filename  string
		timestamp int64
		uid       string
		expected  string
	}{
		{"test.txt", 1234567890, "user123", "test.txt_1234567890_user123"},
		{"file.pdf", 0, "admin", "file.pdf_0_admin"},
		{"", 999999999, "", "_999999999_"},
		{"中文文件.doc", 1609459200, "用户", "中文文件.doc_1609459200_用户"},
		{"file with spaces.txt", 1234567890, "user with spaces", "file with spaces.txt_1234567890_user with spaces"},
	}

	for _, tc := range testCases {
		result := GeneratePathSeed(tc.filename, tc.timestamp, tc.uid)
		assert.Equal(t, tc.expected, result, "路径种子应该正确生成")

		// 验证格式正确性
		parts := strings.Split(result, "_")
		assert.Len(t, parts, 3, "路径种子应该包含3个部分")
		assert.Equal(t, tc.filename, parts[0], "第一部分应该是文件名")
		assert.Equal(t, tc.uid, parts[2], "第三部分应该是用户ID")

		// 验证时间戳部分
		timestampStr := parts[1]
		parsedTimestamp, err := strconv.ParseInt(timestampStr, 10, 64)
		assert.NoError(t, err, "时间戳应该能被解析为整数")
		assert.Equal(t, tc.timestamp, parsedTimestamp, "时间戳应该正确")
	}
}

// 第十五个测试：测试parseSize成功解析各种格式
func TestParseSize_Success(t *testing.T) {
	testCases := []struct {
		input    string
		expected int64
	}{
		// 基本数字
		{"100", 100},
		{"0", 0},
		{"1", 1},

		// 带空格
		{"  100  ", 100},
		{"100 ", 100},
		{" 100", 100},

		// 字节单位
		{"100B", 100},
		{"100b", 100},
		{"100 B", 100},

		// K单位
		{"1K", 1024},
		{"1k", 1024},
		{"2K", 2048},
		{"1KB", 1024},
		{"1kb", 1024},
		{"1 K", 1024},
		{"1 KB", 1024},

		// M单位
		{"1M", 1024 * 1024},
		{"1m", 1024 * 1024},
		{"2M", 2 * 1024 * 1024},
		{"1MB", 1024 * 1024},
		{"1mb", 1024 * 1024},

		// G单位
		{"1G", 1024 * 1024 * 1024},
		{"1g", 1024 * 1024 * 1024},
		{"2G", 2 * 1024 * 1024 * 1024},
		{"1GB", 1024 * 1024 * 1024},

		// T单位
		{"1T", 1024 * 1024 * 1024 * 1024},
		{"1TB", 1024 * 1024 * 1024 * 1024},

		// P单位
		{"1P", 1024 * 1024 * 1024 * 1024 * 1024},
		{"1PB", 1024 * 1024 * 1024 * 1024 * 1024},

		// 小数
		{"1.5K", int64(1.5 * 1024)},
		{"2.5M", int64(2.5 * 1024 * 1024)},
		{"0.5G", int64(0.5 * 1024 * 1024 * 1024)},
	}

	for _, tc := range testCases {
		result, err := parseSize(tc.input)
		assert.NoError(t, err, "解析应该成功: %s", tc.input)
		assert.Equal(t, tc.expected, result, "解析结果应该正确: %s -> %d", tc.input, tc.expected)
	}
}

// 第十六个测试：测试parseSize错误情况
func TestParseSize_Errors(t *testing.T) {
	errorCases := []struct {
		input       string
		expectError string
	}{
		{"", "empty size string"},
		{"   ", "empty size string"},
		{"abc", "invalid number"},
		{"100X", "invalid unit"},
		{"100ZB", "invalid unit"},
		{"100ABC", "invalid unit"},
		{"-100", "size cannot be negative"},
		{"-1M", "size cannot be negative"},
		{"1.5.5M", "invalid number"},
		{"M", "invalid number"},
		{"K100", "invalid number"},
	}

	for _, tc := range errorCases {
		result, err := parseSize(tc.input)
		assert.Error(t, err, "应该返回错误: %s", tc.input)
		assert.Equal(t, int64(0), result, "错误时应该返回0")
		assert.Contains(t, err.Error(), tc.expectError,
			"错误信息应该包含预期内容: %s -> %s", tc.input, err.Error())
	}
}

// 第十七个测试：测试parseSize边界情况
func TestParseSize_EdgeCases(t *testing.T) {
	testCases := []struct {
		input    string
		expected int64
	}{
		{"0B", 0},
		{"0K", 0},
		{"0M", 0},
		{"0.0G", 0},
		{"000", 0},
		{"001", 1},
		{"01K", 1024},
	}

	for _, tc := range testCases {
		result, err := parseSize(tc.input)
		assert.NoError(t, err, "解析应该成功: %s", tc.input)
		assert.Equal(t, tc.expected, result, "解析结果应该正确: %s -> %d", tc.input, tc.expected)
	}
}

// 第十八个测试：测试parseSize大数值处理
func TestParseSize_LargeNumbers(t *testing.T) {
	// 测试接近最大值的情况
	largeValidCases := []struct {
		input    string
		expected int64
	}{
		{"8P", 8 * 1024 * 1024 * 1024 * 1024 * 1024}, // 应该在范围内
		{"1024T", 1024 * 1024 * 1024 * 1024 * 1024},  // 1P
	}

	for _, tc := range largeValidCases {
		result, err := parseSize(tc.input)
		if err != nil {
			// 如果因为溢出而失败，那也是合理的
			assert.Contains(t, err.Error(), "out of range", "大数值溢出应该有相应错误信息")
		} else {
			assert.Equal(t, tc.expected, result, "大数值解析结果应该正确: %s", tc.input)
		}
	}
}

// 第十九个测试：测试parseSize与determineEffectiveLimit集成
func TestParseSize_Integration(t *testing.T) {
	// 这个测试验证parseSize与determineEffectiveLimit的集成
	serverLimit := int64(500 * 1024 * 1024) // 500MB

	// 测试有效的配置字符串
	validConfigs := []struct {
		config   string
		expected int64
	}{
		{"100MB", 100 * 1024 * 1024},
		{"1GB", serverLimit}, // 应该使用服务器限制
		{"200M", 200 * 1024 * 1024},
		{"", serverLimit}, // 空配置使用服务器限制
	}

	for _, tc := range validConfigs {
		result := determineEffectiveLimit(tc.config, serverLimit)
		assert.Equal(t, tc.expected, result,
			"配置 '%s' 应该产生限制 %d", tc.config, tc.expected)
	}
}

// 第二十个测试：测试所有函数的边界和异常情况
func TestUploadHelperFunctions_EdgeCases(t *testing.T) {
	// 测试GenerateFilename的边界情况
	t.Run("GenerateFilename_EdgeCases", func(t *testing.T) {
		// 测试非常长的文件名
		longName := strings.Repeat("a", 200) + ".txt"
		filename, err := GenerateFilename(longName)
		assert.NoError(t, err, "生成长文件名应该成功")
		assert.True(t, strings.HasSuffix(filename, ".txt"), "应该保留扩展名")

		// 测试特殊字符（在扩展名中，但原文件名验证应该在别处）
		specialName := "file.min.js"
		filename, err = GenerateFilename(specialName)
		assert.NoError(t, err, "生成包含特殊字符的文件名应该成功")
		assert.True(t, strings.HasSuffix(filename, ".js"), "应该保留最后的扩展名")
	})

	// 测试GeneratePathSeed的边界情况
	t.Run("GeneratePathSeed_EdgeCases", func(t *testing.T) {
		// 测试特殊字符
		result := GeneratePathSeed("file/with\\slash.txt", 1234567890, "user:with|special")
		expectedParts := []string{"file/with\\slash.txt", "1234567890", "user:with|special"}
		actualParts := strings.Split(result, "_")
		assert.Equal(t, expectedParts, actualParts, "特殊字符应该被保留")

		// 测试极端时间戳
		result = GeneratePathSeed("test.txt", -1, "user")
		assert.Contains(t, result, "_-1_", "负时间戳应该被正确处理")
	})

	// 测试ExecuteWithRetry的边界情况
	t.Run("ExecuteWithRetry_EdgeCases", func(t *testing.T) {
		// 测试零延迟
		callCount := 0
		operation := func() error {
			callCount++
			if callCount < 2 {
				return errors.New("fail")
			}
			return nil
		}

		err := ExecuteWithRetry(operation, 2, 0)
		assert.NoError(t, err, "零延迟重试应该成功")
		assert.Equal(t, 2, callCount, "应该调用2次")
	})
}
