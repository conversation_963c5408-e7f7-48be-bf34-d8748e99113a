package goupload

import (
	"context"
	"strings"
	"testing"

	"github.com/real-rm/gomongo"
	"github.com/stretchr/testify/assert"
)

// TestUploadDirectory_Basic 测试基本的目录上传功能
func TestUploadDirectory_Basic(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.<PERSON>("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TESTBOARD", "documents", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 准备测试文件
	files := []DirectoryFileEntry{
		{
			RelativePath: "doc1.txt",
			Reader:       strings.NewReader("Hello, World! This is document 1."),
			Size:         33,
		},
		{
			RelativePath: "subdir/doc2.txt",
			Reader:       strings.NewReader("Hello, World! This is document 2 in subdirectory."),
			Size:         50,
		},
		{
			RelativePath: "subdir/nested/doc3.txt",
			Reader:       strings.NewReader("Document 3 in nested directory."),
			Size:         32,
		},
	}

	request := &DirectoryUploadRequest{
		Site:          "TESTBOARD",
		EntryName:     "documents",
		UserID:        "user123",
		DirectoryName: "test_documents",
		Files:         files,
		MaxSize:       1024 * 1024, // 1MB
	}

	// 执行上传
	result, err := UploadDirectory(context.Background(), statsUpdater, request)

	// 验证结果
	assert.NoError(t, err, "目录上传应该成功")
	assert.NotNil(t, result, "结果不应该为nil")
	assert.Equal(t, 3, result.TotalFiles, "应该有3个文件")
	assert.Equal(t, int64(115), result.TotalSize, "总大小应该是115字节")
	assert.Len(t, result.SuccessFiles, 3, "应该有3个成功上传的文件")
	assert.Len(t, result.FailedFiles, 0, "不应该有失败的文件")
	assert.False(t, result.IsPartialUpload, "不应该是部分上传")
	assert.NotEmpty(t, result.DirectoryPath, "目录路径不应该为空")

	// 验证文件路径
	expectedFiles := []string{"doc1.txt", "subdir/doc2.txt", "subdir/nested/doc3.txt"}
	assert.ElementsMatch(t, expectedFiles, result.SuccessFiles, "成功上传的文件列表应该匹配")

	// 验证写入路径
	assert.NotEmpty(t, result.WrittenPaths, "应该有写入路径")
}

// TestUploadDirectory_Validation 测试请求验证
func TestUploadDirectory_Validation(t *testing.T) {
	ctx := context.Background()
	mockStats := &MockStatsUpdater{}

	tests := []struct {
		name    string
		request *DirectoryUploadRequest
		wantErr string
	}{
		{
			name:    "nil request",
			request: nil,
			wantErr: "request cannot be nil",
		},
		{
			name: "empty site",
			request: &DirectoryUploadRequest{
				Site:          "",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "site cannot be empty",
		},
		{
			name: "empty entryName",
			request: &DirectoryUploadRequest{
				Site:          "TESTBOARD",
				EntryName:     "",
				UserID:        "user123",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "entryName cannot be empty",
		},
		{
			name: "empty userID",
			request: &DirectoryUploadRequest{
				Site:          "TESTBOARD",
				EntryName:     "documents",
				UserID:        "",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "userID cannot be empty",
		},
		{
			name: "empty directoryName",
			request: &DirectoryUploadRequest{
				Site:          "TESTBOARD",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "",
				Files:         []DirectoryFileEntry{{RelativePath: "test.txt", Reader: strings.NewReader("test")}},
			},
			wantErr: "directoryName cannot be empty",
		},
		{
			name: "empty files",
			request: &DirectoryUploadRequest{
				Site:          "TESTBOARD",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "test",
				Files:         []DirectoryFileEntry{},
			},
			wantErr: "files list cannot be empty",
		},
		{
			name: "path traversal attack",
			request: &DirectoryUploadRequest{
				Site:          "TESTBOARD",
				EntryName:     "documents",
				UserID:        "user123",
				DirectoryName: "test",
				Files: []DirectoryFileEntry{
					{RelativePath: "../../../etc/passwd", Reader: strings.NewReader("test")},
				},
			},
			wantErr: "path traversal not allowed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := UploadDirectory(ctx, mockStats, tt.request)
			assert.Error(t, err, "应该返回错误")
			assert.Contains(t, err.Error(), tt.wantErr, "错误信息应该包含预期内容")
		})
	}
}

// TestUploadDirectory_SizeLimit 测试大小限制
func TestUploadDirectory_SizeLimit(t *testing.T) {
	cleanup := setupUploadIntegrationTest(t)
	defer cleanup()

	// 初始化MongoDB（如果失败则跳过测试）
	err := gomongo.InitMongoDB()
	if err != nil {
		t.Skipf("跳过测试：MongoDB初始化失败: %v", err)
	}

	statsColl := gomongo.Coll("tmp", "dir_stats")
	statsUpdater, err := NewStatsUpdater("TESTBOARD", "documents", statsColl)
	if err != nil {
		t.Skipf("跳过测试：StatsUpdater创建失败: %v", err)
	}

	// 创建超过限制的文件
	largeContent := strings.Repeat("A", 1024) // 1KB
	files := []DirectoryFileEntry{
		{
			RelativePath: "large1.txt",
			Reader:       strings.NewReader(largeContent),
			Size:         1024,
		},
		{
			RelativePath: "large2.txt",
			Reader:       strings.NewReader(largeContent),
			Size:         1024,
		},
	}

	request := &DirectoryUploadRequest{
		Site:          "TESTBOARD",
		EntryName:     "documents",
		UserID:        "user123",
		DirectoryName: "large_test",
		Files:         files,
		MaxSize:       1500, // 1.5KB，应该超过限制
	}

	// 执行上传
	_, err = UploadDirectory(context.Background(), statsUpdater, request)

	// 应该返回大小超限错误
	assert.Error(t, err, "应该返回大小超限错误")
	assert.Contains(t, err.Error(), "exceeds limit", "错误信息应该包含超限信息")
}

// TestValidateRelativePath 测试路径验证
func TestValidateRelativePath(t *testing.T) {
	tests := []struct {
		name    string
		path    string
		wantErr bool
	}{
		{"valid path", "subdir/file.txt", false},
		{"valid nested path", "a/b/c/file.txt", false},
		{"path traversal", "../file.txt", true},
		{"path traversal nested", "subdir/../../../etc/passwd", true},
		{"absolute path", "/etc/passwd", true},
		{"windows absolute path", "C:\\Windows\\System32", true},
		{"too long path", strings.Repeat("a", 501), true},
		{"empty path", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateRelativePath(tt.path)
			if tt.wantErr {
				assert.Error(t, err, "应该返回错误")
			} else {
				assert.NoError(t, err, "不应该返回错误")
			}
		})
	}
}
