package goupload

import (
	"os"
	"path/filepath"
	"sync"
	"testing"
)

var (
	testConfigSetupOnce sync.Once
	testConfigPath      string
)

// SetupTestConfig 设置测试配置环境
// 这个函数应该在所有需要配置的测试开始前调用
func SetupTestConfig(t *testing.T) {
	testConfigSetupOnce.Do(func() {
		// 获取当前工作目录
		wd, err := os.Getwd()
		if err != nil {
			t.Fatalf("Failed to get working directory: %v", err)
		}

		// 设置测试配置文件路径
		testConfigPath = filepath.Join(wd, "test.toml")

		// 检查配置文件是否存在
		if _, err := os.Stat(testConfigPath); os.IsNotExist(err) {
			t.Fatalf("Test config file not found: %s", testConfigPath)
		}

		// 设置环境变量
		os.Setenv("RMBASE_FILE_CFG", testConfigPath)

		// 创建测试所需的目录
		testDirs := []string{
			"test_uploads",
			"test_uploads/files",
			"test_uploads/images",
			"test_uploads/videos",
			"test_uploads/multi",
			"test_uploads/small",
			"test_logs",
			"/tmp/goupload_test",
			"/tmp/goupload_test/files",
			"/tmp/goupload_test/images",
			"/tmp/goupload_test/videos",
			"/tmp/goupload_test/s3",
			"/tmp/goupload_test/multi",
			"/tmp/goupload_test/small",
		}

		for _, dir := range testDirs {
			// 对于绝对路径，直接创建；对于相对路径，在工作目录下创建
			var fullPath string
			if filepath.IsAbs(dir) {
				fullPath = dir
			} else {
				fullPath = filepath.Join(wd, dir)
			}

			if err := os.MkdirAll(fullPath, 0755); err != nil {
				t.Logf("Warning: Failed to create test directory %s: %v", fullPath, err)
				// 不要因为目录创建失败而终止测试，某些目录可能需要特殊权限
			}
		}

		t.Logf("Test configuration setup completed with config file: %s", testConfigPath)
	})

	// 每次调用都设置环境变量，确保测试隔离
	os.Setenv("RMBASE_FILE_CFG", testConfigPath)
}

// CleanupTestConfig 清理测试配置（可选）
func CleanupTestConfig(t *testing.T) {
	// 清理测试目录（可选，通常让系统自动清理）
	testDirs := []string{
		"test_uploads",
		"test_logs",
	}

	wd, err := os.Getwd()
	if err != nil {
		t.Logf("Failed to get working directory for cleanup: %v", err)
		return
	}

	for _, dir := range testDirs {
		fullPath := filepath.Join(wd, dir)
		if err := os.RemoveAll(fullPath); err != nil {
			t.Logf("Warning: Failed to cleanup test directory %s: %v", fullPath, err)
		}
	}
}

// TestMain 可以用于全局测试设置（可选）
// 如果需要在所有测试前后执行某些操作，可以使用这个函数
/*
func TestMain(m *testing.M) {
	// 全局测试前设置
	// ...

	// 运行测试
	code := m.Run()

	// 全局测试后清理
	// ...

	os.Exit(code)
}
*/
