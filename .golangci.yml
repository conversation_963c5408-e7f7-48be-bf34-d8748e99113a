run:
  timeout: 5m
  tests: true
  skip-dirs:
    - testserver

linters-settings:
  govet:
    check-shadowing: true
  gocyclo:
    min-complexity: 15
  dupl:
    threshold: 100
  goconst:
    min-len: 2
    min-occurrences: 2
  misspell:
    locale: US
  lll:
    line-length: 140
  goimports:
    local-prefixes: github.com/real-rm/goupload
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc

linters:
  disable-all: true
  enable:
    - bodyclose
    - depguard
    - dogsled
    - dupl
    - errcheck
    - exportloopref
    - exhaustive
    - gochecknoinits
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - lll
    - misspell
    - nakedret
    - noctx
    - nolintlint
    - rowserrcheck
    - staticcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - whitespace

issues:
  exclude-rules:
    # Exclude some linters from running on tests files.
    - path: _test\.go
      linters:
        - gomnd
        - goconst
        - dupl
        - lll
        - gosec
    # Exclude typecheck for mock-related issues in test files
    - path: _test\.go
      text: "undefined.*mock"
      linters:
        - typecheck
    # Exclude mock method issues
    - path: _test\.go
      text: "(On|Called|AssertExpectations|AssertNotCalled) undefined"
      linters:
        - typecheck
  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0
