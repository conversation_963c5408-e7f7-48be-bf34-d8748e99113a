
[dbs]
verbose = 3

[dbs.tmp]
uri = "mongodb://127.0.0.1:27017/goupload_test"  

[golog]
dir    = "./test_logs"  
level  = "warn"         
format = "text"         

# Global write options for tests
[write_options]
max_retries = 2         # 测试时减少重试次数，加快测试速度
retry_delay = "10ms"    # 测试时使用很短的重试延迟
s3_timeout = "5s"       # 测试时使用较短的超时
chunk_size = 1048576    # 1MB，测试时使用较小的分块大小
enable_logging = false  # 测试时关闭详细日志
validate_content = true
enable_metadata = true

# Test S3 providers (for integration tests using GarageHQ)
[connection_sources]

  [[connection_sources.s3_providers]]
    name = "test-garage-primary"
    endpoint = "http://localhost:3900"  # GarageHQ S3 API 端口
    key = "GK4d1095e87554826d40f6af89"  # 实际生成的主要测试密钥
    pass = "02cca040f63efb8617f28e77d088d0e0535462d045d6139bb5f3649eae0e75a3"
    region = "garage"  # GarageHQ 使用自定义 region

  [[connection_sources.s3_providers]]
    name = "test-garage-secondary"
    endpoint = "http://localhost:3900"
    key = "GK1e8266fe625fd9e3d392d497"  # 实际生成的次要测试密钥
    pass = "0130431df8aa44ddee23777cfdc369b8dab1f51777d4308b49655aa0bdd85bc1"
    region = "garage"


[userupload]
site = "CAR"

  [[userupload.types]]
    entryName = "test_file"
    prefix = "/test/files"
    tmpPath = "/tmp/goupload_test/files"
    maxSize = "100MB"
    storage = [
      { type = "local", path = "test_uploads/files" }
    ]

  [[userupload.types]]
    entryName = "test_image"
    prefix = "/test/images"
    tmpPath = "/tmp/goupload_test/images"
    maxSize = "50MB"
    storage = [
      { type = "local", path = "test_uploads/images" }
    ]

  [[userupload.types]]
    entryName = "test_video"
    prefix = "/test/videos"
    tmpPath = "/tmp/goupload_test/videos"
    maxSize = "500MB"
    storage = [
      { type = "local", path = "test_uploads/videos" }
    ]

  [[userupload.types]]
    entryName = "test_s3_only"
    prefix = "/test/s3"
    tmpPath = "/tmp/goupload_test/s3"
    maxSize = "200MB"
    storage = [
      { type = "s3", target = "test-garage-primary", bucket = "test-bucket" }
    ]

  [[userupload.types]]
    entryName = "test_multi_storage"
    prefix = "/test/multi"
    tmpPath = "/tmp/goupload_test/multi"
    maxSize = "300MB"
    storage = [
      { type = "local", path = "test_uploads/multi" },
      { type = "s3", target = "test-garage-primary", bucket = "test-multi-bucket" }
    ]

  [[userupload.types]]
    entryName = "test_small_limit"
    prefix = "/test/small"
    tmpPath = "/tmp/goupload_test/small"
    maxSize = "1KB"  # 用于测试大小限制
    storage = [
      { type = "local", path = "test_uploads/small" }
    ]



